// 前端测试全局设置
// 启动前端开发服务器和后端API服务器

import { spawn, ChildProcess } from 'child_process';
import axios from 'axios';

let backendProcess: ChildProcess | null = null;
let frontendProcess: ChildProcess | null = null;

async function globalSetup() {
  console.log('🚀 启动测试环境...');

  // 启动后端服务器
  console.log('📡 启动后端API服务器...');
  backendProcess = spawn('node', ['start-server.js'], {
    cwd: process.cwd() + '/../backend',
    stdio: 'pipe',
    detached: false
  });

  backendProcess.stdout?.on('data', (data) => {
    console.log(`[Backend] ${data.toString().trim()}`);
  });

  backendProcess.stderr?.on('data', (data) => {
    console.error(`[Backend Error] ${data.toString().trim()}`);
  });

  // 等待后端服务器启动
  await waitForServer('http://localhost:3001/health', 30000);
  console.log('✅ 后端API服务器启动成功');

  // 启动前端开发服务器
  console.log('🎨 启动前端开发服务器...');
  const npmCommand = process.platform === 'win32' ? 'npm.cmd' : 'npm';
  frontendProcess = spawn(npmCommand, ['run', 'dev'], {
    cwd: process.cwd(),
    stdio: 'pipe',
    detached: false,
    env: { ...process.env, PORT: '3002' },
    shell: true
  });

  frontendProcess.stdout?.on('data', (data) => {
    console.log(`[Frontend] ${data.toString().trim()}`);
  });

  frontendProcess.stderr?.on('data', (data) => {
    console.error(`[Frontend Error] ${data.toString().trim()}`);
  });

  // 等待前端服务器启动 (尝试多个端口)
  let frontendUrl = '';
  for (const port of [3002, 3003, 3004, 3005]) {
    try {
      await waitForServer(`http://localhost:${port}`, 10000);
      frontendUrl = `http://localhost:${port}`;
      console.log(`✅ 前端开发服务器启动成功: ${frontendUrl}`);
      break;
    } catch (error) {
      // 继续尝试下一个端口
    }
  }

  if (!frontendUrl) {
    throw new Error('前端服务器未能在任何端口启动');
  }

  // 保存前端URL到全局变量
  (globalThis as any).__FRONTEND_URL__ = frontendUrl;

  // 保存进程引用到全局变量
  (globalThis as any).__BACKEND_PROCESS__ = backendProcess;
  (globalThis as any).__FRONTEND_PROCESS__ = frontendProcess;
}

async function waitForServer(url: string, timeout: number = 30000): Promise<boolean> {
  const startTime = Date.now();

  while (Date.now() - startTime < timeout) {
    try {
      const response = await axios.get(url, { timeout: 5000 });
      if (response.status === 200) {
        return true;
      }
    } catch (error) {
      // 服务器还未启动，继续等待
    }

    // 等待2秒后重试
    await new Promise(resolve => setTimeout(resolve, 2000));
  }

  throw new Error(`服务器 ${url} 在 ${timeout}ms 内未能启动`);
}

export default globalSetup;
