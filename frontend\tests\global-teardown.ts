// 前端测试全局清理
// 关闭前端和后端服务器

import { ChildProcess } from 'child_process';

async function globalTeardown() {
  console.log('🛑 关闭测试环境...');

  const backendProcess = (globalThis as any).__BACKEND_PROCESS__ as ChildProcess;
  const frontendProcess = (globalThis as any).__FRONTEND_PROCESS__ as ChildProcess;

  // 关闭前端服务器
  if (frontendProcess && !frontendProcess.killed) {
    console.log('🛑 关闭前端开发服务器...');
    frontendProcess.kill('SIGTERM');

    await new Promise<void>((resolve) => {
      frontendProcess.on('exit', () => {
        console.log('✅ 前端开发服务器已关闭');
        resolve();
      });

      setTimeout(() => {
        if (!frontendProcess.killed) {
          frontendProcess.kill('SIGKILL');
          console.log('⚠️ 强制关闭前端开发服务器');
        }
        resolve();
      }, 5000);
    });
  }

  // 关闭后端服务器
  if (backendProcess && !backendProcess.killed) {
    console.log('🛑 关闭后端API服务器...');
    backendProcess.kill('SIGTERM');

    await new Promise<void>((resolve) => {
      backendProcess.on('exit', () => {
        console.log('✅ 后端API服务器已关闭');
        resolve();
      });

      setTimeout(() => {
        if (!backendProcess.killed) {
          backendProcess.kill('SIGKILL');
          console.log('⚠️ 强制关闭后端API服务器');
        }
        resolve();
      }, 5000);
    });
  }

  console.log('✅ 测试环境清理完成');
}

export default globalTeardown;
