// Playwright全局设置
// 在所有测试开始前启动后端服务

const { spawn } = require('child_process');
const axios = require('axios');

let serverProcess = null;

async function globalSetup() {
  console.log('🚀 启动后端服务器进行测试...');
  
  // 启动后端服务器
  serverProcess = spawn('node', ['start-server.js'], {
    cwd: __dirname + '/..',
    stdio: 'pipe',
    detached: false
  });

  // 监听服务器输出
  serverProcess.stdout.on('data', (data) => {
    console.log(`[Server] ${data.toString().trim()}`);
  });

  serverProcess.stderr.on('data', (data) => {
    console.error(`[Server Error] ${data.toString().trim()}`);
  });

  // 等待服务器启动
  await waitForServer('http://localhost:3001/health', 30000);
  
  console.log('✅ 后端服务器启动成功');
  
  // 将进程ID保存到全局变量
  global.__SERVER_PROCESS__ = serverProcess;
}

async function waitForServer(url, timeout = 30000) {
  const startTime = Date.now();
  
  while (Date.now() - startTime < timeout) {
    try {
      const response = await axios.get(url, { timeout: 5000 });
      if (response.status === 200) {
        return true;
      }
    } catch (error) {
      // 服务器还未启动，继续等待
    }
    
    // 等待1秒后重试
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  throw new Error(`服务器在 ${timeout}ms 内未能启动`);
}

module.exports = globalSetup;
