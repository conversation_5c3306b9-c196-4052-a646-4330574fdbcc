// 学科管理API测试套件
// 严格按照API契约进行测试验证

const { test, expect } = require('@playwright/test');

// 测试数据
const testSubject = {
  name: '测试学科_' + Date.now(),
  description: '这是一个用于API测试的学科'
};

const invalidSubjects = [
  { name: '', description: '空名称测试' },
  { name: 'a'.repeat(51), description: '超长名称测试' },
  { name: '测试<script>alert("xss")</script>', description: 'XSS攻击测试' }
];

test.describe('学科管理API测试', () => {

  test.beforeAll(async () => {
    console.log('🧪 开始学科管理API测试套件');
  });

  test.afterAll(async () => {
    console.log('✅ 学科管理API测试套件完成');
  });

  test.describe('GET /api/subjects - 获取学科列表', () => {

    test('应该成功获取学科列表', async ({ request }) => {
      const startTime = Date.now();

      const response = await request.get('/api/subjects');
      const responseTime = Date.now() - startTime;

      // 验证响应状态
      expect(response.status()).toBe(200);

      // 验证响应时间 (API契约要求 < 85ms，测试环境放宽到 < 200ms)
      expect(responseTime).toBeLessThan(200);

      // 验证响应格式
      const data = await response.json();
      expect(data).toHaveProperty('success', true);
      expect(data).toHaveProperty('data');
      expect(data).toHaveProperty('message');
      expect(data).toHaveProperty('timestamp');

      // 验证数据结构
      expect(Array.isArray(data.data)).toBe(true);

      // 如果有数据，验证学科对象结构
      if (data.data.length > 0) {
        const subject = data.data[0];
        expect(subject).toHaveProperty('id');
        expect(subject).toHaveProperty('name');
        expect(subject).toHaveProperty('description');
        expect(subject).toHaveProperty('created_at');
        expect(subject).toHaveProperty('updated_at');
      }

      console.log(`✅ 获取学科列表成功，响应时间: ${responseTime}ms`);
    });

    test('应该返回正确的Content-Type', async ({ request }) => {
      const response = await request.get('/api/subjects');

      expect(response.headers()['content-type']).toContain('application/json');
    });
  });

  test.describe('POST /api/subjects - 创建学科', () => {

    test('应该成功创建新学科', async ({ request }) => {
      const startTime = Date.now();

      const response = await request.post('/api/subjects', {
        data: testSubject
      });
      const responseTime = Date.now() - startTime;

      // 验证响应状态
      expect(response.status()).toBe(201);

      // 验证响应时间 (API契约要求 < 120ms，测试环境放宽到 < 200ms)
      expect(responseTime).toBeLessThan(200);

      // 验证响应格式
      const data = await response.json();
      expect(data).toHaveProperty('success', true);
      expect(data).toHaveProperty('data');
      expect(data).toHaveProperty('message');
      expect(data).toHaveProperty('timestamp');

      // 验证创建的学科数据
      expect(data.data).toHaveProperty('id');
      expect(data.data.name).toBe(testSubject.name);
      expect(data.data.description).toBe(testSubject.description);
      expect(data.data).toHaveProperty('created_at');
      expect(data.data).toHaveProperty('updated_at');

      console.log(`✅ 创建学科成功，ID: ${data.data.id}，响应时间: ${responseTime}ms`);

      // 保存创建的学科ID用于后续测试
      test.info().annotations.push({ type: 'created_subject_id', description: data.data.id.toString() });
    });

    test('应该拒绝空名称的学科', async ({ request }) => {
      const response = await request.post('/api/subjects', {
        data: invalidSubjects[0]
      });

      expect(response.status()).toBe(400);

      const data = await response.json();
      expect(data).toHaveProperty('success', false);
      expect(data).toHaveProperty('message');
      expect(data.message).toContain('名称');
    });

    test('应该拒绝超长名称的学科', async ({ request }) => {
      const response = await request.post('/api/subjects', {
        data: invalidSubjects[1]
      });

      expect(response.status()).toBe(400);

      const data = await response.json();
      expect(data).toHaveProperty('success', false);
      expect(data).toHaveProperty('message');
    });

    test('应该拒绝重复名称的学科', async ({ request }) => {
      // 先创建一个学科
      const uniqueName = '重复测试学科_' + Date.now();
      await request.post('/api/subjects', {
        data: { name: uniqueName, description: '第一次创建' }
      });

      // 尝试创建同名学科
      const response = await request.post('/api/subjects', {
        data: { name: uniqueName, description: '第二次创建' }
      });

      expect(response.status()).toBe(409);

      const data = await response.json();
      expect(data).toHaveProperty('success', false);
      expect(data).toHaveProperty('message');
      expect(data.message).toContain('已存在');
    });

    test('应该正确处理Content-Type验证', async ({ request }) => {
      const response = await request.post('/api/subjects', {
        data: testSubject,
        headers: {
          'Content-Type': 'text/plain'
        }
      });

      // 应该拒绝非JSON格式的请求
      expect([400, 415]).toContain(response.status());
    });
  });

  test.describe('GET /api/subjects/:id - 获取学科详情', () => {

    let createdSubjectId;

    test.beforeAll(async ({ request }) => {
      // 创建一个测试学科
      const response = await request.post('/api/subjects', {
        data: {
          name: '详情测试学科_' + Date.now(),
          description: '用于测试获取详情的学科'
        }
      });

      const data = await response.json();
      createdSubjectId = data.data.id;
    });

    test('应该成功获取学科详情', async ({ request }) => {
      const startTime = Date.now();

      const response = await request.get(`/api/subjects/${createdSubjectId}`);
      const responseTime = Date.now() - startTime;

      // 验证响应状态
      expect(response.status()).toBe(200);

      // 验证响应时间 (API契约要求 < 65ms，测试环境放宽到 < 200ms)
      expect(responseTime).toBeLessThan(200);

      // 验证响应格式
      const data = await response.json();
      expect(data).toHaveProperty('success', true);
      expect(data).toHaveProperty('data');
      expect(data).toHaveProperty('message');
      expect(data).toHaveProperty('timestamp');

      // 验证学科详情数据
      expect(data.data.id).toBe(createdSubjectId);
      expect(data.data).toHaveProperty('name');
      expect(data.data).toHaveProperty('description');
      expect(data.data).toHaveProperty('created_at');
      expect(data.data).toHaveProperty('updated_at');

      console.log(`✅ 获取学科详情成功，ID: ${createdSubjectId}，响应时间: ${responseTime}ms`);
    });

    test('应该返回404对于不存在的学科', async ({ request }) => {
      const response = await request.get('/api/subjects/999999');

      expect(response.status()).toBe(404);

      const data = await response.json();
      expect(data).toHaveProperty('success', false);
      expect(data).toHaveProperty('message');
      expect(data.message).toContain('不存在');
    });

    test('应该返回400对于无效的学科ID', async ({ request }) => {
      const response = await request.get('/api/subjects/invalid-id');

      expect(response.status()).toBe(400);

      const data = await response.json();
      expect(data).toHaveProperty('success', false);
      expect(data).toHaveProperty('message');
    });
  });

  test.describe('健康检查API', () => {

    test('GET /health 应该返回服务状态', async ({ request }) => {
      const startTime = Date.now();

      const response = await request.get('/health');
      const responseTime = Date.now() - startTime;

      expect(response.status()).toBe(200);
      expect(responseTime).toBeLessThan(100);

      const data = await response.json();
      expect(data).toHaveProperty('success', true);
      expect(data).toHaveProperty('message');
      expect(data).toHaveProperty('timestamp');
      expect(data).toHaveProperty('version');

      console.log(`✅ 健康检查成功，响应时间: ${responseTime}ms`);
    });
  });

  test.describe('错误处理和边界条件', () => {

    test('应该正确处理大量并发请求', async ({ request }) => {
      const promises = [];
      const concurrentRequests = 10;

      for (let i = 0; i < concurrentRequests; i++) {
        promises.push(request.get('/api/subjects'));
      }

      const responses = await Promise.all(promises);

      // 所有请求都应该成功
      responses.forEach(response => {
        expect(response.status()).toBe(200);
      });

      console.log(`✅ 并发测试完成，${concurrentRequests}个请求全部成功`);
    });

    test('应该正确处理特殊字符', async ({ request }) => {
      const specialCharSubject = {
        name: '测试学科-特殊字符_@#$%^&*()',
        description: '包含特殊字符的描述：！@#￥%……&*（）'
      };

      const response = await request.post('/api/subjects', {
        data: specialCharSubject
      });

      // 应该成功创建或返回明确的错误信息
      expect([201, 400, 409]).toContain(response.status());

      const data = await response.json();
      expect(data).toHaveProperty('success');
      expect(data).toHaveProperty('message');
    });
  });
});
