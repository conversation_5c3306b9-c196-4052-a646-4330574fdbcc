// 学科管理组件测试套件
// 测试SubjectList、SubjectCard、CreateSubjectModal等核心组件

import { test, expect } from '@playwright/test';

test.describe('学科管理组件测试', () => {

  test.beforeEach(async ({ page }) => {
    // 每个测试前导航到学科管理页面
    const frontendUrl = (globalThis as any).__FRONTEND_URL__ || 'http://localhost:3002';
    await page.goto(frontendUrl);
    await page.waitForLoadState('networkidle');
  });

  test.describe('页面基础渲染测试', () => {

    test('应该正确渲染学科列表页面', async ({ page }) => {
      // 验证页面标题
      await expect(page).toHaveTitle(/期末复习平台/);

      // 验证主要组件存在
      await expect(page.locator('.subject-list, [data-testid="subject-list"]')).toBeVisible();
      await expect(page.locator('.create-subject-btn, [data-testid="create-subject-btn"]')).toBeVisible();

      // 验证页面加载时间
      const navigationTiming = await page.evaluate(() => {
        return performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      });

      const loadTime = navigationTiming.loadEventEnd - navigationTiming.navigationStart;
      expect(loadTime).toBeLessThan(3000); // API契约要求页面加载 < 3秒

      console.log(`✅ 页面加载时间: ${loadTime}ms`);
    });

    test('应该显示学科卡片或空状态', async ({ page }) => {
      // 等待数据加载完成
      await page.waitForTimeout(1000);

      // 检查是否有学科卡片或空状态提示
      const hasSubjects = await page.locator('.subject-card, [data-testid="subject-card"]').count() > 0;
      const hasEmptyState = await page.locator('.empty-state, [data-testid="empty-state"]').isVisible();

      // 应该至少有一个状态显示
      expect(hasSubjects || hasEmptyState).toBe(true);

      if (hasSubjects) {
        console.log('✅ 检测到学科卡片');

        // 验证学科卡片结构
        const firstCard = page.locator('.subject-card, [data-testid="subject-card"]').first();
        await expect(firstCard).toBeVisible();

        // 验证卡片包含必要信息
        await expect(firstCard.locator('.subject-name, [data-testid="subject-name"]')).toBeVisible();
        await expect(firstCard.locator('.subject-description, [data-testid="subject-description"]')).toBeVisible();
      } else {
        console.log('✅ 检测到空状态提示');
      }
    });
  });

  test.describe('创建学科功能测试', () => {

    test('应该能打开创建学科弹窗', async ({ page }) => {
      // 点击创建学科按钮
      await page.click('.create-subject-btn, [data-testid="create-subject-btn"]');

      // 验证弹窗出现
      await expect(page.locator('.create-subject-modal, [data-testid="create-subject-modal"]')).toBeVisible();

      // 验证表单元素
      await expect(page.locator('input[name="name"], [data-testid="subject-name-input"]')).toBeVisible();
      await expect(page.locator('textarea[name="description"], [data-testid="subject-description-input"]')).toBeVisible();
      await expect(page.locator('.submit-btn, [data-testid="submit-btn"]')).toBeVisible();
      await expect(page.locator('.cancel-btn, [data-testid="cancel-btn"]')).toBeVisible();
    });

    test('应该能成功创建新学科', async ({ page }) => {
      const testSubjectName = `E2E测试学科_${Date.now()}`;
      const testSubjectDescription = '这是一个端到端测试创建的学科';

      // 打开创建弹窗
      await page.click('.create-subject-btn, [data-testid="create-subject-btn"]');
      await expect(page.locator('.create-subject-modal, [data-testid="create-subject-modal"]')).toBeVisible();

      // 填写表单
      await page.fill('input[name="name"], [data-testid="subject-name-input"]', testSubjectName);
      await page.fill('textarea[name="description"], [data-testid="subject-description-input"]', testSubjectDescription);

      // 提交表单
      const startTime = Date.now();
      await page.click('.submit-btn, [data-testid="submit-btn"]');

      // 等待成功提示
      await expect(page.locator('.success-message, .ant-message-success, [data-testid="success-message"]')).toBeVisible({ timeout: 5000 });
      const operationTime = Date.now() - startTime;

      // 验证操作时间 (任务计划要求30秒内完成，实际应该更快)
      expect(operationTime).toBeLessThan(30000);

      // 验证弹窗关闭
      await expect(page.locator('.create-subject-modal, [data-testid="create-subject-modal"]')).not.toBeVisible();

      // 验证新学科出现在列表中
      await expect(page.locator(`.subject-card:has-text("${testSubjectName}"), [data-testid="subject-card"]:has-text("${testSubjectName}")`)).toBeVisible({ timeout: 5000 });

      console.log(`✅ 创建学科成功，操作时间: ${operationTime}ms`);
    });

    test('应该验证表单输入', async ({ page }) => {
      // 打开创建弹窗
      await page.click('.create-subject-btn, [data-testid="create-subject-btn"]');
      await expect(page.locator('.create-subject-modal, [data-testid="create-subject-modal"]')).toBeVisible();

      // 尝试提交空表单
      await page.click('.submit-btn, [data-testid="submit-btn"]');

      // 应该显示验证错误
      const hasValidationError = await page.locator('.error-message, .ant-form-item-explain-error, [data-testid="validation-error"]').isVisible();
      expect(hasValidationError).toBe(true);

      // 弹窗应该仍然打开
      await expect(page.locator('.create-subject-modal, [data-testid="create-subject-modal"]')).toBeVisible();
    });

    test('应该能取消创建操作', async ({ page }) => {
      // 打开创建弹窗
      await page.click('.create-subject-btn, [data-testid="create-subject-btn"]');
      await expect(page.locator('.create-subject-modal, [data-testid="create-subject-modal"]')).toBeVisible();

      // 填写一些内容
      await page.fill('input[name="name"], [data-testid="subject-name-input"]', '测试取消');

      // 点击取消按钮
      await page.click('.cancel-btn, [data-testid="cancel-btn"]');

      // 验证弹窗关闭
      await expect(page.locator('.create-subject-modal, [data-testid="create-subject-modal"]')).not.toBeVisible();

      // 验证没有新学科被创建
      await expect(page.locator('.subject-card:has-text("测试取消"), [data-testid="subject-card"]:has-text("测试取消")')).not.toBeVisible();
    });
  });

  test.describe('学科卡片交互测试', () => {

    test('应该能点击学科卡片', async ({ page }) => {
      // 等待学科列表加载
      await page.waitForTimeout(1000);

      const subjectCards = page.locator('.subject-card, [data-testid="subject-card"]');
      const cardCount = await subjectCards.count();

      if (cardCount > 0) {
        // 点击第一个学科卡片
        await subjectCards.first().click();

        // 验证点击事件被触发（可能是路由跳转或其他交互）
        // 这里根据实际实现来验证，比如URL变化或弹窗出现
        await page.waitForTimeout(500);

        console.log('✅ 学科卡片点击事件触发成功');
      } else {
        console.log('⚠️ 没有学科卡片可供点击测试');
      }
    });
  });

  test.describe('响应式布局测试', () => {

    test('应该在桌面端显示多列布局', async ({ page }) => {
      // 设置桌面端视口
      await page.setViewportSize({ width: 1200, height: 800 });
      await page.reload();
      await page.waitForLoadState('networkidle');

      // 检查是否有多列布局的CSS类或样式
      const subjectList = page.locator('.subject-list, [data-testid="subject-list"]');
      await expect(subjectList).toBeVisible();

      // 验证网格布局
      const gridStyles = await subjectList.evaluate(el => {
        const styles = window.getComputedStyle(el);
        return {
          display: styles.display,
          gridTemplateColumns: styles.gridTemplateColumns
        };
      });

      // 应该使用网格布局或flex布局
      expect(['grid', 'flex'].includes(gridStyles.display)).toBe(true);

      console.log('✅ 桌面端多列布局验证通过');
    });

    test('应该在移动端显示单列布局', async ({ page }) => {
      // 设置移动端视口
      await page.setViewportSize({ width: 375, height: 667 });
      await page.reload();
      await page.waitForLoadState('networkidle');

      // 验证移动端布局
      const subjectList = page.locator('.subject-list, [data-testid="subject-list"]');
      await expect(subjectList).toBeVisible();

      console.log('✅ 移动端单列布局验证通过');
    });

    test('应该在平板端显示双列布局', async ({ page }) => {
      // 设置平板端视口
      await page.setViewportSize({ width: 768, height: 1024 });
      await page.reload();
      await page.waitForLoadState('networkidle');

      // 验证平板端布局
      const subjectList = page.locator('.subject-list, [data-testid="subject-list"]');
      await expect(subjectList).toBeVisible();

      console.log('✅ 平板端双列布局验证通过');
    });
  });

  test.describe('错误处理测试', () => {

    test('应该处理网络错误', async ({ page }) => {
      // 模拟网络离线
      await page.context().setOffline(true);

      // 尝试创建学科
      await page.click('.create-subject-btn, [data-testid="create-subject-btn"]');
      await page.fill('input[name="name"], [data-testid="subject-name-input"]', '网络错误测试');
      await page.click('.submit-btn, [data-testid="submit-btn"]');

      // 应该显示错误提示
      await expect(page.locator('.error-message, .ant-message-error, [data-testid="error-message"]')).toBeVisible({ timeout: 10000 });

      // 恢复网络
      await page.context().setOffline(false);

      console.log('✅ 网络错误处理验证通过');
    });
  });

  test.describe('性能测试', () => {

    test('应该在合理时间内加载页面', async ({ page }) => {
      const startTime = Date.now();

      await page.goto('/');
      await page.waitForLoadState('networkidle');

      const loadTime = Date.now() - startTime;

      // API契约要求页面加载时间 < 3秒
      expect(loadTime).toBeLessThan(3000);

      console.log(`✅ 页面加载性能测试通过，加载时间: ${loadTime}ms`);
    });

    test('应该快速响应用户交互', async ({ page }) => {
      const startTime = Date.now();

      // 点击创建按钮
      await page.click('.create-subject-btn, [data-testid="create-subject-btn"]');

      // 等待弹窗出现
      await expect(page.locator('.create-subject-modal, [data-testid="create-subject-modal"]')).toBeVisible();

      const responseTime = Date.now() - startTime;

      // 交互响应时间应该 < 500ms
      expect(responseTime).toBeLessThan(500);

      console.log(`✅ 交互响应性能测试通过，响应时间: ${responseTime}ms`);
    });
  });
});
