// Playwright全局清理
// 在所有测试结束后关闭后端服务

async function globalTeardown() {
  console.log('🛑 关闭后端服务器...');
  
  const serverProcess = global.__SERVER_PROCESS__;
  
  if (serverProcess && !serverProcess.killed) {
    // 优雅关闭服务器
    serverProcess.kill('SIGTERM');
    
    // 等待进程结束
    await new Promise((resolve) => {
      serverProcess.on('exit', () => {
        console.log('✅ 后端服务器已关闭');
        resolve();
      });
      
      // 如果5秒内没有关闭，强制杀死进程
      setTimeout(() => {
        if (!serverProcess.killed) {
          serverProcess.kill('SIGKILL');
          console.log('⚠️ 强制关闭后端服务器');
        }
        resolve();
      }, 5000);
    });
  }
}

module.exports = globalTeardown;
